<?php

declare(strict_types=1);

namespace App\classes;

use Exception;
use PDO;
use PDOException;

class Idioma
{
    // --- Atributos ---
    private ?int    $id     = null;
    private ?string $nombre = null;
    private ?int    $estado = null;

    /**
     * Constructor: Inicializa las propiedades del objeto Idioma.
     */
    public function __construct()
    {
        $this->id     = null; // null para nuevos objetos sin ID
        $this->nombre = null;
        $this->estado = 1;    // Estado activo por defecto (siguiendo convención lunex)
    }

    /**
     * Método estático para construir un objeto Idioma desde un array (ej. fila de DB).
     *
     * @param array $resultado Array asociativo con los datos del idioma.
     *
     * @return self Instancia de Idioma.
     * @throws Exception Si ocurre un error durante la construcción.
     */
    public static function construct(array $resultado = []): self
    {
        try {
            $objeto         = new self();
            $objeto->id     = isset($resultado['id']) ? (int)$resultado['id'] : null;
            $objeto->nombre = $resultado['nombre'] ?? null;
            $objeto->estado = isset($resultado['estado']) ? (int)$resultado['estado'] : 1;
            return $objeto;
        } catch (Exception $e) {
            throw new Exception("Error al construir objeto Idioma: " . $e->getMessage());
        }
    }

    /**
     * Obtiene un idioma por su ID.
     *
     * @param int $id       ID del idioma.
     * @param PDO $conexion Conexión PDO.
     *
     * @return self|null Objeto Idioma o null si no se encuentra.
     * @throws Exception Si hay error en DB.
     */
    public static function get_by_id(int $id, PDO $conexion): ?self
    {
        try {
            // Consulta para obtener idioma por ID (Usando Heredoc)
            $query = <<<SQL
            SELECT
            	*
            FROM idiomas
            WHERE
            	id = :id
            LIMIT 1
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(":id", $id, PDO::PARAM_INT);
            $statement->execute();
            $resultado = $statement->fetch(PDO::FETCH_ASSOC);

            return $resultado ? self::construct($resultado) : null;

        } catch (PDOException $e) {
            throw new Exception("Error al obtener Idioma por ID: " . $e->getMessage());
        }
    }

    /**
     * Obtiene una lista de idiomas activos.
     *
     * @param PDO $conexion Conexión PDO.
     *
     * @return array Array de objetos Idioma.
     * @throws Exception Si hay error en DB.
     */
    public static function get_list(PDO $conexion): array
    {
        try {
            // Consulta para obtener lista de idiomas activos (Usando Heredoc)
            $query = <<<SQL
            SELECT
            	*
            FROM idiomas
            WHERE
            	estado = 1
            ORDER BY
            	nombre
            SQL;

            $statement = $conexion->prepare($query);
            $statement->execute();
            $resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

            $listado = [];
            foreach ($resultados as $resultado) {
                $listado[] = self::construct($resultado);
            }
            return $listado;

        } catch (PDOException $e) {
            throw new Exception("Error al obtener lista de Idiomas: " . $e->getMessage());
        }
    }

    /**
     * Obtiene una lista de idiomas filtrada por parámetros.
     *
     * @param array $filtros Filtros de búsqueda.
     * @param PDO $conexion Conexión PDO.
     *
     * @return array Array de objetos Idioma.
     * @throws Exception Si hay error en DB.
     */
    public static function get_list_filtered(array $filtros, PDO $conexion): array
    {
        try {
            $query = "SELECT * FROM idiomas WHERE 1=1";
            $params = [];

            if (!empty($filtros['nombre'])) {
                $query .= " AND nombre LIKE :nombre";
                $params[':nombre'] = '%' . $filtros['nombre'] . '%';
            }

            if (isset($filtros['estado']) && $filtros['estado'] !== '') {
                $query .= " AND estado = :estado";
                $params[':estado'] = $filtros['estado'];
            }

            $query .= " ORDER BY nombre";

            $statement = $conexion->prepare($query);
            $statement->execute($params);
            $resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

            $listado = [];
            foreach ($resultados as $resultado) {
                $listado[] = self::construct($resultado);
            }
            return $listado;

        } catch (PDOException $e) {
            throw new Exception("Error al obtener lista de Idiomas filtrada: " . $e->getMessage());
        }
    }

    /**
     * Crea un nuevo idioma en la base de datos a partir de un objeto Idioma.
     *
     * @param PDO $conexion Conexión PDO.
     *
     * @return int|false El ID del nuevo idioma creado o false en caso de error.
     * @throws Exception Si los datos requeridos en el objeto están vacíos o hay error en DB.
     */
    function crear(PDO $conexion): int|false
    {
        // Validaciones básicas sobre el objeto
        if (empty($this->getNombre())) {
            throw new Exception("El nombre es requerido en el objeto Idioma para crearlo.");
        }

        try {
            $nombre = $this->getNombre(); // Para usar en mensaje de error

            // Preparar la consulta INSERT usando Heredoc
            $query = <<<SQL
            INSERT INTO idiomas (
            	 nombre
            	,estado
            ) VALUES (
            	 :nombre
            	,:estado
            )
            SQL;

            $statement = $conexion->prepare($query);

            // Bind de parámetros desde el objeto
            $statement->bindValue(':nombre', $this->getNombre(), PDO::PARAM_STR);
            $statement->bindValue(':estado', $this->getEstado() ?? 1, PDO::PARAM_INT);

            // Ejecutar la consulta
            $success = $statement->execute();

            if ($success) {
                // Devolver el ID del idioma recién creado
                return (int)$conexion->lastInsertId();
            } else {
                return false; // Error en la ejecución
            }

        } catch (PDOException $e) {
            // Manejar errores específicos de DB (ej. nombre duplicado)
            if ($e->getCode() == 23000 || $e->getCode() == 1062) { // Códigos comunes para violación de UNIQUE constraint
                throw new Exception("Error al crear idioma: El nombre '$nombre' ya existe.");
            } else {
                throw new Exception("Error de base de datos al crear idioma: " . $e->getMessage());
            }
        } catch (Exception $e) {
            // Capturar otros errores
            throw new Exception("Error al crear idioma: " . $e->getMessage());
        }
    }

    /**
     * Actualiza un idioma existente en la base de datos.
     *
     * @param PDO $conexion Conexión PDO.
     *
     * @return bool True si la actualización fue exitosa, False en caso contrario.
     * @throws Exception Si los datos requeridos están vacíos o hay error en DB.
     */
    function actualizar(PDO $conexion): bool
    {
        // Validaciones básicas
        if ($this->getId() === null || $this->getId() <= 0) {
            throw new Exception("ID válido es requerido para actualizar el idioma.");
        }
        if (empty($this->getNombre())) {
            throw new Exception("El nombre no puede estar vacío.");
        }

        try {
            // Consulta para actualizar el idioma
            $query = <<<SQL
            UPDATE idiomas SET
                nombre = :nombre_update,
                estado = :estado_update
            WHERE
                id = :id_update
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(':nombre_update', $this->getNombre(), PDO::PARAM_STR);
            $statement->bindValue(':estado_update', $this->getEstado() ?? 1, PDO::PARAM_INT);
            $statement->bindValue(':id_update', $this->getId(), PDO::PARAM_INT);

            return $statement->execute();

        } catch (PDOException $e) {
            throw new Exception("Error de base de datos al actualizar idioma (ID: {$this->getId()}): " . $e->getMessage());
        }
    }

    /**
     * Elimina un idioma de la base de datos (eliminación física).
     *
     * @param int $id       ID del idioma a eliminar.
     * @param PDO $conexion Conexión PDO.
     *
     * @return bool True si la eliminación fue exitosa, False en caso contrario.
     * @throws Exception Si ocurre un error de base de datos.
     */
    public static function eliminar(int $id, PDO $conexion): bool
    {
        try {
            // Consulta para eliminar el idioma físicamente
            $query = <<<SQL
            DELETE FROM idiomas
            WHERE
            	id = :id_delete
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(':id_delete', $id, PDO::PARAM_INT);

            return $statement->execute();

        } catch (PDOException $e) {
            throw new Exception("Error de base de datos al eliminar idioma (ID: $id): " . $e->getMessage());
        }
    }

    /**
     * Desactiva un idioma estableciendo su estado a 0.
     *
     * @param int $id       ID del idioma a desactivar.
     * @param PDO $conexion Conexión PDO.
     *
     * @return bool True si la desactivación fue exitosa, False en caso contrario.
     * @throws Exception Si ocurre un error de base de datos.
     */
    public static function desactivar(int $id, PDO $conexion): bool
    {
        try {
            // Consulta para actualizar el estado a 0 (inactivo)
            $query = <<<SQL
            UPDATE idiomas SET
            	estado = 0
            WHERE
            	id = :id_desactivar
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(':id_desactivar', $id, PDO::PARAM_INT);

            return $statement->execute();

        } catch (PDOException $e) {
            throw new Exception("Error de base de datos al desactivar idioma (ID: $id): " . $e->getMessage());
        }
    }

    // --- Getters y Setters ---

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(?int $id): self
    {
        $this->id = $id;
        return $this;
    }

    public function getNombre(): ?string
    {
        return $this->nombre;
    }

    public function setNombre(?string $nombre): self
    {
        $this->nombre = $nombre;
        return $this;
    }

    public function getEstado(): ?int
    {
        return $this->estado;
    }

    public function setEstado(?int $estado): self
    {
        $this->estado = $estado;
        return $this;
    }

    // --- Métodos adicionales ---

    /**
     * Verifica si el idioma está activo.
     * @return bool
     */
    public function isActiva(): bool
    {
        // Asegúrate de que el estado no sea null antes de comparar
        return $this->estado === 1;
    }
}
