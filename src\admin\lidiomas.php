<?php

// Iniciar sesión si es necesario
use App\classes\Idioma;

if (session_status() === PHP_SESSION_NONE) {
	session_start();
}

/** @var PDO $conexion */
global $conexion;

require_once dirname(__FILE__, 3) . '/config/config.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/general/preparar.php';

// Check for valid database connection before proceeding
if (!$conexion instanceof PDO) {
	error_log("Error crítico: No hay conexión a la base de datos en lidiomas.php.");
	$response['message'] = 'Error crítico: No se pudo conectar a la base de datos.';
	http_response_code(503); // Service Unavailable
	echo json_encode($response);
	exit;
}

#region region init variables
$idiomas         = []; // Initialize as an empty array
$error_display   = '';
$error_text      = '';
$success_display = '';
$success_text    = '';
$filtro_nombre   = '';
$filtro_estado   = '';
#endregion init variables

#region region Handle Flash Message Success
// Check for a success flash message from session
if (!empty($_SESSION['flash_message_success'])) {
	$success_text    = $_SESSION['flash_message_success'];
	$success_display = 'show';
	// Clear the flash message so it doesn't show again
	unset($_SESSION['flash_message_success']);
}
#endregion Handle Flash Message Success

#region region Handle Flash Message Error
// Check for an error flash message from session
if (!empty($_SESSION['flash_message_error'])) {
	$error_text    = $_SESSION['flash_message_error'];
	$error_display = 'show';
	// Clear the flash message so it doesn't show again
	unset($_SESSION['flash_message_error']);
}
#endregion Handle Flash Message Error

#region region Handle Filter
// Handle filter parameters
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
	$filtro_nombre = filter_input(INPUT_POST, 'nombre', FILTER_SANITIZE_SPECIAL_CHARS) ?? '';
    $filtro_estado = filter_input(INPUT_POST, 'estado', FILTER_SANITIZE_SPECIAL_CHARS) ?? '';
}
#endregion Handle Filter

#region region Create idioma
// --- Handle AJAX Request (Create Idioma) ---
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] == 'crear') {
	header('Content-Type: application/json');                            // Set response header for JSON
	$response = ['success' => false, 'message' => 'Error desconocido.']; // Default error response
	
	$nombre = filter_input(INPUT_POST, 'nombre', FILTER_SANITIZE_SPECIAL_CHARS);
	
	// Validate required fields
	if (empty($nombre)) {
		$response['message'] = 'Error: El nombre es requerido.';
		echo json_encode($response);
		exit;
	}
	
	try {
		// Create new idioma object
		$idioma = new Idioma();
		$idioma->setNombre($nombre);
		
		// Save the idioma
		$newId = $idioma->crear($conexion);
		
		if ($newId) {
			$response = [
				'success'   => true,
				'message'   => 'Idioma creado correctamente.',
				'idioma' => [
					'id'     => $newId,
					'nombre' => $idioma->getNombre(),
                    'estado' => 1
				]
			];
		} else {
			$response['message'] = 'Error: No se pudo crear el idioma.';
		}
		
	} catch (Exception $e) {
		$response['message'] = 'Error: ' . $e->getMessage();
	}
	
	echo json_encode($response);
	exit;
}
#endregion Create idioma

#region region Modify idioma
// --- Handle AJAX Request (Modify Idioma) ---
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] == 'modificar') {
	header('Content-Type: application/json');                            // Set response header for JSON
	$response = ['success' => false, 'message' => 'Error desconocido.']; // Default error response
	
	$idiomaId = filter_input(INPUT_POST, 'idiomaId', FILTER_VALIDATE_INT);
	$nombre      = filter_input(INPUT_POST, 'nombre', FILTER_SANITIZE_SPECIAL_CHARS);
	
	// Validate required fields
	if (!$idiomaId) {
		$response['message'] = 'Error: ID de idioma inválido.';
		echo json_encode($response);
		exit;
	}
	
	if (empty($nombre)) {
		$response['message'] = 'Error: El nombre es requerido.';
		echo json_encode($response);
		exit;
	}
	
	try {
		// Get the existing idioma
		$idioma = Idioma::get_by_id($idiomaId, $conexion);
		
		if (!$idioma) {
			$response['message'] = 'Error: Idioma no encontrado.';
			echo json_encode($response);
			exit;
		}
		
		// Update the idioma properties
		$idioma->setNombre($nombre);
		
		// Save the changes
		$success = $idioma->actualizar($conexion);
		
		if ($success) {
			$response = [
				'success'   => true,
				'message'   => 'Idioma actualizado correctamente.',
				'idioma' => [
					'id'     => $idioma->getId(),
					'nombre' => $idioma->getNombre(),
				]
			];
		} else {
			$response['message'] = 'Error: No se pudo actualizar el idioma.';
		}
		
	} catch (Exception $e) {
		$response['message'] = 'Error: ' . $e->getMessage();
	}
	
	echo json_encode($response);
	exit;
}
#endregion Modify idioma

#region region Deactivate idioma
// --- Handle POST Request (Deactivate Idioma) ---
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] == 'desactivar') {
	$idiomaIdToDeactivate = filter_input(INPUT_POST, 'idiomaId', FILTER_VALIDATE_INT);
	
	if ($idiomaIdToDeactivate) {
		try {
			$success = Idioma::desactivar($idiomaIdToDeactivate, $conexion);
			
			if ($success) {
				$_SESSION['flash_message_success'] = "Idioma desactivado correctamente.";
			} else {
				$_SESSION['flash_message_error'] = "Error: No se pudo encontrar o desactivar el idioma.";
			}
		} catch (Exception $e) {
			$_SESSION['flash_message_error'] = "Error al desactivar idioma: " . $e->getMessage();
		}
	} else {
		$_SESSION['flash_message_error'] = "Error: ID de idioma inválido para desactivar.";
	}
	
	// Redirect back to the idioma list page after processing
	header('Location: listado-idiomas');
	exit;
}
#endregion Deactivate idioma

#region try
try {
	// Prepare filter parameters
	$parametros = [];
	if (!empty($filtro_nombre)) {
		$parametros['nombre'] = $filtro_nombre;
	}
    if ($filtro_estado !== '') {
        $parametros['estado'] = $filtro_estado;
    }
	
	// Get filtered list of providers
	if (!empty($parametros)) {
		$idiomas = Idioma::get_list_filtered($parametros, $conexion);
	} else {
		$idiomas = Idioma::get_list($conexion);
	}
	
} catch (PDOException $e) {
	// Specific handling for database errors
	$error_display = 'show';
	$error_text    = "Error de base de datos al obtener la lista de idiomas.";
} catch (Exception $e) {
	// General error handling
	$error_display = 'show';
	$error_text    = "Ocurrió un error inesperado al obtener la lista de idiomas: " . $e->getMessage();
}
#endregion try

require_once __ROOT__ . '/views/admin/lidiomas.view.php';
