<?php



/** @var PDO $conexion */
global $conexion;

require_once dirname(__FILE__, 2) . '/config/config.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/classes/phpmailercorreo.php';
require_once __ROOT__ . '/src/general/preparar_web.php';


use App\classes\HusoHorario;
use App\classes\Freelancer;
use App\classes\FreelancerExperiencia;
use App\classes\FreelancerDisponibilidad;

use App\classes\FreelancerReferencia;
use App\classes\FreelancerTarifa;
use App\classes\FreelancerDocumento;
use App\classes\FreelancerIdioma;
use App\classes\Idioma;
use App\classes\ServicioCategoria;
use App\classes\Servicio;

require_once __ROOT__ . '/vendor/autoload.php';

// Check for valid database connection before proceeding
if (!$conexion instanceof PDO) {
	error_log("Error crítico: No hay conexión a la base de datos en form_aliados_comerciales.php (POST).");
	$response['message'] = 'Error crítico: No se pudo conectar a la base de datos.';
	http_response_code(503); // Service Unavailable
	echo json_encode($response);
	exit;
}

#region region init variables
$form_errors  = [];
$form_success = null;
$form_data    = $_POST;
#endregion init variables
#region get
if ($_SERVER['REQUEST_METHOD'] == 'GET') {
	try {
	
	
	} catch (Exception $e) {
		$error_display = 'show';
		$error_text    = $e->getMessage();
	}
}
#endregion get
#region region POST
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
	$is_ajax_request = isset($_POST['is_ajax']) && $_POST['is_ajax'] == '1';
	
	try {
		// Set the default timezone for date functions
		date_default_timezone_set('America/Bogota');

		// 1. Recoger y limpiar datos
		$form_data['nombre_completo']            = limpiar_datos($_POST['nombre_completo'] ?? '');
		$form_data['tipo_documento']             = limpiar_datos($_POST['tipo_documento'] ?? '');
		$form_data['documento_identidad']        = limpiar_datos($_POST['documento_identidad'] ?? '');
		$form_data['correo_electronico']         = limpiar_datos($_POST['correo_electronico'] ?? '');
		$form_data['numero_telefono']            = limpiar_datos($_POST['numero_telefono'] ?? '');
		$form_data['direccion_completa']         = limpiar_datos($_POST['direccion_completa'] ?? '');
		$form_data['ciudad_residencia']          = limpiar_datos($_POST['ciudad_residencia'] ?? '');
		$form_data['pais_residencia']            = limpiar_datos($_POST['pais_residencia'] ?? '');
		$form_data['resumen_profesional']        = limpiar_datos($_POST['resumen_profesional'] ?? '');
		$form_data['certificaciones_relevantes'] = limpiar_datos($_POST['certificaciones_relevantes'] ?? '');

		// --- Data Retrieval (Services Selection) with Deduplication ---
		$servicios_seleccionados = [];
		if (isset($_POST['servicios']) && is_array($_POST['servicios'])) {
			$servicios_raw = $_POST['servicios'];
			error_log("Raw services received: " . json_encode($servicios_raw));

			$servicios_seleccionados = array_map('intval', $servicios_raw);
			$servicios_seleccionados = array_filter($servicios_seleccionados, function($id) {
				return $id > 0;
			});

			$servicios_before_dedup = $servicios_seleccionados;
			// Remove duplicates that can occur when both desktop and mobile checkboxes are submitted
			$servicios_seleccionados = array_unique($servicios_seleccionados);
			// Re-index the array to ensure clean sequential keys
			$servicios_seleccionados = array_values($servicios_seleccionados);

			error_log("Services before deduplication: " . json_encode($servicios_before_dedup));
			error_log("Services after deduplication: " . json_encode($servicios_seleccionados));
		}
		$form_data['servicios']                  = $servicios_seleccionados;                        // Deduplicated array de service IDs

		// --- Data Retrieval (Languages Selection) ---
		$idiomas_seleccionados = [];
		if (isset($_POST['idiomas']) && is_array($_POST['idiomas'])) {
			foreach ($_POST['idiomas'] as $idioma_data) {
				if (isset($idioma_data['id_idioma']) && isset($idioma_data['nivel'])) {
					$id_idioma = filter_var($idioma_data['id_idioma'], FILTER_VALIDATE_INT);
					$nivel = trim($idioma_data['nivel']);

					if ($id_idioma && !empty($nivel)) {
						$idiomas_seleccionados[] = [
							'id_idioma' => $id_idioma,
							'nivel' => $nivel
						];
					}
				}
			}
		}
		$form_data['idiomas'] = $idiomas_seleccionados;

		$form_data['disponibilidad_tiempo']      = $_POST['disponibilidad_tiempo'] ?? [];          // Array de strings
		$form_data['modalidad_colaboracion']     = $_POST['modalidad_colaboracion'] ?? [];         // Array de strings
		$form_data['id_huso_horario']            = limpiar_datos($_POST['id_huso_horario'] ?? ''); // ID de HusoHorario
		$form_data['tarifa_por_hora']            = limpiar_datos($_POST['tarifa_por_hora'] ?? ''); // Puede ser vacío

		$form_data['proyectos_realizados']       = limpiar_datos($_POST['proyectos_realizados'] ?? '');
		$form_data['nombre_completo_ref1']       = limpiar_datos($_POST['nombre_completo_ref1'] ?? '');
		$form_data['numero_telefono_ref1']       = limpiar_datos($_POST['numero_telefono_ref1'] ?? '');
		$form_data['nombre_completo_ref2']       = limpiar_datos($_POST['nombre_completo_ref2'] ?? '');
		$form_data['numero_telefono_ref2']       = limpiar_datos($_POST['numero_telefono_ref2'] ?? '');
		$form_data['veracidad_informacion']      = isset($_POST['veracidad_informacion']) ? 1 : 0;
		$form_data['consentimiento_datos']       = isset($_POST['consentimiento_datos']) ? 1 : 0;
		$form_data['acuerdo_terminos']           = isset($_POST['acuerdo_terminos']) ? 1 : 0;
		// Manejo de archivos (solo nombres por ahora, la lógica real es más compleja)
		$cv_file_info   = $_FILES['cv_file'] ?? null;
		$cert_file_info = $_FILES['certificaciones_file'] ?? null;
		
		// 2. Validar datos en el servidor
		#region region validaciones
		if (empty($form_data['nombre_completo'])) {
			$form_errors['nombre_completo'] = 'El nombre completo es requerido.';
		}
		if (empty($form_data['tipo_documento'])) {
			$form_errors['tipo_documento'] = 'Selecciona un tipo de documento.';
		}
		if (empty($form_data['documento_identidad'])) {
			$form_errors['documento_identidad'] = 'El número de documento es requerido.';
		}
		// Validar unicidad de documento o correo si es necesario (requiere consulta DB)
		if (empty($form_data['correo_electronico'])) {
			$form_errors['correo_electronico'] = 'El correo electrónico es requerido.';
		} elseif (!filter_var($form_data['correo_electronico'], FILTER_VALIDATE_EMAIL)) {
			$form_errors['correo_electronico'] = 'El formato del correo no es válido.';
		}
		if (empty($form_data['numero_telefono'])) {
			$form_errors['numero_telefono'] = 'El número de teléfono es requerido.';
		}
		// Añadir más validaciones para dirección, ciudad, país, linkedin (formato URL), etc.
		if (empty($form_data['resumen_profesional'])) {
			$form_errors['resumen_profesional'] = 'El resumen profesional es requerido.';
		}
		if (empty($form_data['certificaciones_relevantes'])) {
			$form_errors['certificaciones_relevantes'] = 'Las certificaciones son requeridas (o indica "Ninguna").';
		}
		if (empty($form_data['servicios'])) {
			$form_errors['servicios'] = 'Debe seleccionar al menos un servicio.';
		}
		if (empty($form_data['disponibilidad_tiempo'])) {
			$form_errors['disponibilidad_tiempo'] = 'Selecciona al menos una disponibilidad de tiempo.';
		}
		if (empty($form_data['modalidad_colaboracion'])) {
			$form_errors['modalidad_colaboracion'] = 'Selecciona al menos una modalidad de colaboración.';
		}
		if (empty($form_data['id_huso_horario'])) {
			$form_errors['id_huso_horario'] = 'Selecciona una zona horaria.';
		}
		if (!empty($form_data['tarifa_por_hora']) && !is_numeric($form_data['tarifa_por_hora'])) {
			$form_errors['tarifa_por_hora'] = 'La tarifa debe ser un valor numérico.';
		}

		if (empty($form_data['nombre_completo_ref1'])) {
			$form_errors['nombre_completo_ref1'] = 'El nombre de la referencia 1 es requerido.';
		}
		if (empty($form_data['numero_telefono_ref1'])) {
			$form_errors['numero_telefono_ref1'] = 'El contacto de la referencia 1 es requerido.';
		}
		// Validar archivos subidos (existencia, errores, tamaño, tipo)
		if (empty($cv_file_info) || $cv_file_info['error'] !== UPLOAD_ERR_OK) {
			$form_errors['cv_file'] = 'Es obligatorio adjuntar el CV.';
		} elseif ($cv_file_info['size'] > 5 * 1024 * 1024) {
			$form_errors['cv_file'] = 'El CV no puede pesar más de 5MB.';
		} // 5MB limit
		// Validar tipo de CV (pdf, doc, docx) - requiere chequear mime type o extensión
		// Validar archivo de certificaciones si se subió
		if (!empty($cert_file_info) && $cert_file_info['error'] === UPLOAD_ERR_OK) {
			if ($cert_file_info['size'] > 5 * 1024 * 1024) {
				$form_errors['certificaciones_file'] = 'El archivo de certificaciones no puede pesar más de 5MB.';
			}
			// Validar tipo PDF
			$finfo     = finfo_open(FILEINFO_MIME_TYPE);
			$mime_type = finfo_file($finfo, $cert_file_info['tmp_name']);
			finfo_close($finfo);
			if ($mime_type !== 'application/pdf') {
				$form_errors['certificaciones_file'] = 'Solo se permiten archivos PDF para certificaciones.';
			}
		} elseif (!empty($cert_file_info) && $cert_file_info['error'] !== UPLOAD_ERR_NO_FILE && $cert_file_info['error'] !== UPLOAD_ERR_OK) {
			$form_errors['certificaciones_file'] = 'Hubo un error al subir el archivo de certificaciones.';
		}
		
		if (empty($form_data['veracidad_informacion'])) {
			$form_errors['veracidad_informacion'] = 'Debes declarar la veracidad de la información.';
		}
		if (empty($form_data['consentimiento_datos'])) {
			$form_errors['consentimiento_datos'] = 'Debes autorizar el tratamiento de datos.';
		}
		if (empty($form_data['acuerdo_terminos'])) {
			$form_errors['acuerdo_terminos'] = 'Debes aceptar los términos y condiciones.';
		}
		#endregion validaciones
		
		// 3. Si no hay errores, procesar y guardar
		if (empty($form_errors)) {
			try {
				$conexion->beginTransaction();
				
				// --- Crear y guardar Freelancer (Necesitarás la clase Freelancer) ---
				$freelancer = new Freelancer();
				// Usar setters para poblar el objeto $freelancer con $form_data
				$freelancer->setNombreCompleto($form_data['nombre_completo']);
				$freelancer->setTipoDocumento($form_data['tipo_documento']);
				$freelancer->setDocumentoIdentidad($form_data['documento_identidad']);
				$freelancer->setCorreoElectronico($form_data['correo_electronico']);
				$freelancer->setNumeroTelefono($form_data['numero_telefono']);
				$freelancer->setDireccionCompleta($form_data['direccion_completa']);
				$freelancer->setCiudadResidencia($form_data['ciudad_residencia']);
				$freelancer->setPaisResidencia($form_data['pais_residencia']);
				$freelancer->setLeyoConsentimiento($form_data['acuerdo_terminos']);
				
				// --- Asignar Freelancer Experiencia ---
				$experiencia = new FreelancerExperiencia();
				$experiencia->setResumenProfesional($form_data['resumen_profesional']);
				$experiencia->setCertificacionesRelevantes($form_data['certificaciones_relevantes']);
				$experiencia->setProyectosRelevantes($form_data['proyectos_realizados']);
				
				// -- Crear y asignar objetos RangoExperiencia y NivelEducativo (solo con ID) ---

				
				// --- Poblar Disponibilidad ---
				$disponibilidad = new FreelancerDisponibilidad();
				$disponibilidad->setModalidadProyecto(in_array('proyecto', $form_data['modalidad_colaboracion']));
				$disponibilidad->setModalidadContratoLargoPlazo(in_array('largo_plazo', $form_data['modalidad_colaboracion']));
				$disponibilidad->setModalidadHibrido(in_array('hibrido', $form_data['modalidad_colaboracion']));
				$disponibilidad->setDisponibilidadTiempoCompleto(in_array('completo', $form_data['disponibilidad_tiempo']));
				$disponibilidad->setDisponibilidadMedioTiempo(in_array('medio', $form_data['disponibilidad_tiempo']));
				$disponibilidad->setDisponibilidadPorHoras(in_array('horas', $form_data['disponibilidad_tiempo']));
				
				// Asignar Huso Horario
				$huso = new HusoHorario();
				$huso->setId((int)$form_data['id_huso_horario']);
				$disponibilidad->setHusoHorario($huso);
				

				
				// --- Poblar Referencias ---
				// Crear y poblar el objeto de referencias
				$referencias = new FreelancerReferencia();
				$referencias->setFreelancer($freelancer);                   // Asociar el objeto Freelancer completo
				$referencias->setNombreCompletoRef1($form_data['nombre_completo_ref1']);
				$referencias->setNumeroTelefonoRef1($form_data['numero_telefono_ref1']);
				$referencias->setNombreCompletoRef2($form_data['nombre_completo_ref2'] ?: null);
				$referencias->setNumeroTelefonoRef2($form_data['numero_telefono_ref2'] ?: null);
				// --- Fin Poblar Referencias ---
				
				// --- Poblar Tarifa (Objeto Relacionado) --- *** NUEVO ***
				$tarifa = new FreelancerTarifa();
				$tarifa->setTarifaPorHora($form_data['tarifa_por_hora']);
				
				// -- Asociar objetos relacionados a Freelancer --
				$freelancer->setExperiencia($experiencia);
				$freelancer->setDisponibilidad($disponibilidad);

				$freelancer->setReferencias($referencias);
				$freelancer->setTarifa($tarifa);
				
				// ----- Crear Freelancer -----
				$freelancer->guardar($conexion);

				if ($freelancer->getId() == 0) {
					throw new Exception("No se pudo guardar la información principal del freelancer.");
				} else {
					$freelancer_id = $freelancer->getId();
				}

				// ----- Guardar Servicios -----
				if (!empty($form_data['servicios'])) {
					if (!$freelancer->guardarServicios($form_data['servicios'], $conexion)) {
						throw new Exception("No se pudieron guardar los servicios del freelancer.");
					}
				}

				// ----- Guardar Idiomas -----
				if (!empty($form_data['idiomas'])) {
					foreach ($form_data['idiomas'] as $idioma_data) {
						$freelancerIdioma = new FreelancerIdioma();
						$freelancerIdioma->setIdFreelancer($freelancer_id);
						$freelancerIdioma->setIdIdioma($idioma_data['id_idioma']);
						$freelancerIdioma->setNivel($idioma_data['nivel']);

						$idioma_id = $freelancerIdioma->crear($conexion);
						if (!$idioma_id) {
							throw new Exception("No se pudo guardar el idioma: " . $idioma_data['nivel']);
						}
					}
				}

				// ----- Manejo de Archivos (Subida y asignación de rutas al objeto $documentos) -----
				$documentos = new FreelancerDocumento();
				
				$upload_dir = FreelancerDocumento::URL_CARPETA;
				if (!is_dir($upload_dir)) {
					if (!mkdir($upload_dir, 0755, true)) {
						throw new Exception("Error: No se pudo crear el directorio de subidas: $upload_dir");
					}
				}
				
				$cv_path_db = null;
				if ($cv_file_info && $cv_file_info['error'] === UPLOAD_ERR_OK) {
					$ext         = strtolower(pathinfo($cv_file_info['name'], PATHINFO_EXTENSION));
					$cv_filename = "cv_" . $freelancer_id . "_" . uniqid() . "." . $ext;
					$destination = $upload_dir . $cv_filename;
					if (!move_uploaded_file($cv_file_info['tmp_name'], $destination)) {
						throw new Exception("Error al mover el archivo CV subido a '$destination'. Verifica permisos.");
					}
					$cv_path_db = $cv_filename;
					// Asignar al objeto Documentos
					$documentos->setCurriculum($cv_path_db);
				}
				
				$cert_path_db = null;
				if ($cert_file_info && $cert_file_info['error'] === UPLOAD_ERR_OK) {
					$ext           = strtolower(pathinfo($cert_file_info['name'], PATHINFO_EXTENSION));
					$cert_filename = "cert_" . $freelancer_id . "_" . uniqid() . "." . $ext;
					$destination   = $upload_dir . $cert_filename;
					if (!move_uploaded_file($cert_file_info['tmp_name'], $destination)) {
						throw new Exception("Error al mover el archivo de certificaciones subido a '$destination'. Verifica permisos.");
					}
					$cert_path_db = $cert_filename;
					// Asignar al objeto Documentos
					$documentos->setCertificaciones($cert_path_db);
				}
				
				// Guardar Documentos (con las rutas ya asignadas)
				$documentos->setFreelancer($freelancer);
				
				if (!$documentos->guardar($conexion)) {
					throw new Exception("No se pudo guardar la información de documentos.");
				}
				// --- Fin Manejo de Archivos ---
				
				// Si todo fue bien, confirmar transacción
				$conexion->commit();
				$form_success = "¡Perfil de Freelancer registrado con éxito! Gracias por unirte a nuestro equipo.";

				// --- Enviar correo de bienvenida ---
				try {
					$email_params = [
						'nombre_freelancer' => $freelancer->getNombreCompleto(), // Use data from the saved object
						'correo_freelancer' => $freelancer->getCorreoElectronico()
					];
					PHPMailerCorreo::enviar_correo_freelancer($email_params);
				} catch (Exception $email_ex) {
					// Log email error but don't stop the success response for the form
					error_log("Error al enviar correo de bienvenida a freelancer ID {$freelancer->getId()}: " . $email_ex->getMessage());
				}
				// --- Fin Enviar correo de bienvenida ---
				
				// --- Enviar correo de notificación a gerencia ---
				try {
					$gerencia_params = [
						'nombre_freelancer'   => $freelancer->getNombreCompleto(),
						'correo_freelancer'   => $freelancer->getCorreoElectronico(),
						'telefono_freelancer' => $freelancer->getNumeroTelefono()
					];
					PHPMailerCorreo::enviar_correo_gerencia_freelancer($gerencia_params, $conexion);
				} catch (Exception $gerencia_ex) {
					// Log email error but don't stop the success response for the form
					error_log("Error al enviar correo a gerencia sobre nuevo freelancer ID {$freelancer->getId()}: " . $gerencia_ex->getMessage());
				}
				// --- Fin Enviar correo de notificación a gerencia ---

				// Limpiar datos para la vista después de enviar correo
				$_POST        = [];
				$form_data    = [];
				
				// *** RESPUESTA AJAX ÉXITO ***
				if ($is_ajax_request) {
					header('Content-Type: application/json');
					echo json_encode(['success' => true, 'message' => $form_success]);
					exit; // Detener ejecución para no enviar HTML
				}
				
			} catch (Exception $e) {
				$conexion->rollBack();
				$error_message = "No se pudo guardar el perfil: " . $e->getMessage();
				error_log("Error al guardar freelancer: " . $e->getMessage() . "\n" . $e->getTraceAsString());
				$form_errors['general'] = $error_message;
				
				// *** RESPUESTA AJAX ERROR INTERNO ***
				if ($is_ajax_request) {
					header('Content-Type: application/json');
					http_response_code(500); // Error interno del servidor
					echo json_encode(['success' => false, 'message' => $error_message, 'errors' => $form_errors]);
					exit; // Detener ejecución para no enviar HTML
				}
			}
		} else {
			// Si hay errores de validación inicial
			$validation_error_message = "Por favor, corrige los errores indicados en el formulario.";
			$form_errors['general']   = $validation_error_message;
			
			// *** RESPUESTA AJAX ERROR VALIDACIÓN ***
			if ($is_ajax_request) {
				header('Content-Type: application/json');
				http_response_code(400); // Bad Request (error de cliente)
				echo json_encode(['success' => false, 'message' => $validation_error_message, 'errors' => $form_errors]);
				exit; // Detener ejecución para no enviar HTML
			}
		}
	} catch (Exception $e) {
		$critical_error_message = "Error crítico en el procesamiento: " . $e->getMessage();
		error_log($critical_error_message . "\n" . $e->getTraceAsString());
		$form_errors['general'] = $critical_error_message;
		
		// Intentar enviar respuesta AJAX si es posible identificarla
		if (isset($_POST['is_ajax']) && $_POST['is_ajax'] == '1') {
			header('Content-Type: application/json');
			http_response_code(500);
			echo json_encode(['success' => false, 'message' => $critical_error_message]);
			exit;
		}
	}
}
#endregion POST
#region try
try {
	//--- Carga de datos para la vista (GET y POST no AJAX) ---
	// Esta parte solo se ejecuta si NO es una petición AJAX que ya terminó con exit;

	$husos_horarios      = Husohorario::get_list($conexion);
	$idiomas             = Idioma::get_list($conexion);

	// Load service categories and services data for the form
	// Obtener todas las categorías de servicios activas ordenadas por prioridad
	$categorias_servicios = ServicioCategoria::get_list($conexion);

	// Optimized: Get all services in a single query instead of N+1 queries
	$todos_servicios = Servicio::get_list($conexion);

	// Group services by category for efficient assignment
	$servicios_por_categoria = [];
	foreach ($todos_servicios as $servicio) {
		$id_categoria = $servicio->getId_servicio_categoria();
		if (!isset($servicios_por_categoria[$id_categoria])) {
			$servicios_por_categoria[$id_categoria] = [];
		}
		$servicios_por_categoria[$id_categoria][] = $servicio;
	}

	// Assign services to their respective categories
	foreach ($categorias_servicios as $categoria) {
		$id_categoria = $categoria->getId();
		$categoria->setServicios($servicios_por_categoria[$id_categoria] ?? []);
	}

} catch (Exception $e) {
	$form_errors['carga_datos'] = "Error al cargar opciones del formulario: " . $e->getMessage();
	$husos_horarios = [];
	$categorias_servicios = [];
}
#endregion try

require_once __ROOT__ . '/views/form_freelancers.view.php';

?>
