<?php

declare(strict_types=1);

/**
 * AJAX endpoint for searching Idioma records for autocomplete functionality.
 * Used in the freelancer form for language selection.
 * 
 * Follows the pattern established by search_modulos_ajax.php for consistency.
 */

use App\classes\Idioma;

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

/** @var PDO $conexion */
global $conexion;

// Include necessary files
require_once dirname(__DIR__, 2) . '/config/config.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/general/preparar.php';

// Set content type to JSON
header('Content-Type: application/json');

// Check for valid database connection
if (!$conexion instanceof PDO) {
    echo json_encode(['success' => false, 'message' => 'Error de conexión a la base de datos']);
    exit;
}

try {
    // Validate request method
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception("Método de solicitud no válido.");
    }

    // Get search parameters from POST
    $search_query = trim($_POST['query'] ?? '');
    $limit = filter_input(INPUT_POST, 'limit', FILTER_VALIDATE_INT) ?: 20;

    // Validate required parameters
    if (empty($search_query) || strlen($search_query) < 1) {
        echo json_encode([
            'success' => true,
            'results' => [],
            'message' => 'Escriba al menos 1 caracter para buscar.'
        ]);
        exit;
    }

    // Search languages using a custom search method
    $idiomas = Idioma::searchForAutocomplete($search_query, $limit, $conexion);

    // Format results for autocomplete
    $results = [];
    foreach ($idiomas as $idioma) {
        $results[] = [
            'id' => $idioma->getId(),
            'nombre' => $idioma->getNombre(),
            'display_text' => $idioma->getNombre()
        ];
    }

    echo json_encode(['success' => true, 'results' => $results]);

} catch (Exception $e) {
    error_log("Error in search_idiomas_ajax.php: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Error al buscar idiomas: ' . $e->getMessage()]);
}
