<?php
#region region DOCS

/** @var Idioma[] $idiomas */
/** @var string $filtro_nombre */
/** @var string $filtro_estado */
/** @var string $success_text */
/** @var string $success_display */
/** @var string $error_text */
/** @var string $error_display */

use App\classes\Idioma;

#endregion DOCS
?>
<!DOCTYPE html>
<html lang="es" class="dark-mode">
<head>
    <meta charset="utf-8"/>
    <title><?php echo APP_NAME; ?> | Idiomas</title>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
    <meta content="Gestión de idiomas" name="description"/>
    <meta content="" name="author"/>

    <?php #region HEAD ?>
    <?php require_once __ROOT__ . '/views/admin/general/head.view.php'; ?>
    <?php #endregion HEAD ?>

    <!-- Estilos adicionales específicos para esta página -->
    <style>
        /* Toast notification styling */
        .toast-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1055;
        }

        .toast {
            min-width: 300px;
        }

        .toast-success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }

        .toast-error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
    </style>
</head>
<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
    <span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed">
    <!-- #header -->
    <?php require_once __ROOT__ . '/views/admin/general/header.view.php'; ?>

    <!-- #sidebar -->
    <?php require_once __ROOT__ . '/views/admin/general/sidebar.view.php'; ?>

    <!-- BEGIN #content -->
    <div id="content" class="app-content">
        <?php #region region PAGE HEADER ?>
        <div class="d-flex align-items-center mb-3">
            <div>
                <h4 class="mb-0">Gestión de Idiomas</h4>
                <p class="mb-0 text-muted">Administra los idiomas del sistema</p>
            </div>
            <div class="ms-auto">
                <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#crearIdiomaModal">
                    <i class="fa fa-plus-circle fa-fw me-1"></i> Nuevo Idioma
                </button>
            </div>
        </div>

        <hr>
        <?php #endregion PAGE HEADER ?>

        <?php #region region FILTERS ?>
        <div class="panel panel-inverse mt-3 no-border-radious">
            <div class="panel-heading no-border-radious">
                <h4 class="panel-title">Filtros</h4>
            </div>
            <div class="panel-body">
                <form method="POST" action="listado-idiomas" class="row g-3">
                    <div class="col-md-4">
                        <label for="nombre" class="form-label">Nombre del Idioma</label>
                        <input type="text" class="form-control" id="nombre" name="nombre"
                               value="<?php echo htmlspecialchars($filtro_nombre); ?>"
                               placeholder="Buscar por nombre...">
                    </div>
                    <div class="col-md-3">
                        <label for="estado" class="form-label">Estado</label>
                        <select class="form-select" id="estado" name="estado">
                            <option value="" <?php echo ($filtro_estado === '') ? 'selected' : ''; ?>>Todos</option>
                            <option value="1" <?php echo ($filtro_estado === '1') ? 'selected' : ''; ?>>Activo</option>
                            <option value="0" <?php echo ($filtro_estado === '0') ? 'selected' : ''; ?>>Inactivo</option>
                        </select>
                    </div>
                    <div class="col-md-2 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary me-2">
                            <i class="fa fa-search"></i> Filtrar
                        </button>
                        <a href="listado-idiomas" class="btn btn-secondary">
                            <i class="fa fa-times"></i> Limpiar
                        </a>
                    </div>
                </form>
            </div>
        </div>
        <?php #endregion FILTERS ?>

        <?php #region region PANEL IDIOMAS ?>
        <div class="panel panel-inverse mt-3 no-border-radious">
            <div class="panel-heading no-border-radious">
                <h4 class="panel-title">
                    Listado de Idiomas
                    <?php if (!empty($filtro_nombre)): ?>
                        <small class="text-muted">(Filtrado por: "<?php echo htmlspecialchars($filtro_nombre); ?>")</small>
                    <?php endif; ?>
                </h4>
            </div>
            <!-- BEGIN PANEL body -->
            <div class="p-1 table-nowrap" style="overflow: auto">
                <?php #region region TABLE IDIOMAS ?>
                <table class="table table-hover table-sm">
                    <thead>
                    <tr>
                        <th>Acciones</th>
                        <th>ID</th>
                        <th>Nombre</th>
                        <th>Estado</th>
                    </tr>
                    </thead>
                    <tbody class="fs-12px" id="idioma-table-body">
                    <?php foreach ($idiomas as $idioma): ?>
                        <tr data-idioma-id="<?php echo $idioma->getId(); ?>">
                            <td>
                                <?php // Edit Button ?>
                                <button type="button" class="btn btn-xs btn-primary me-1 btn-editar-idioma"
                                        title="Editar Idioma"
                                        data-idiomaid="<?php echo $idioma->getId(); ?>"
                                        data-nombre="<?php echo htmlspecialchars($idioma->getNombre() ?? ''); ?>">
                                    <i class="fa fa-edit"></i>
                                </button>
                                <?php // Deactivate Button ?>
                                <button type="button" class="btn btn-xs btn-danger btn-desactivar-idioma"
                                        title="Desactivar"
                                        data-idiomaid="<?php echo $idioma->getId(); ?>"
                                        data-nombre="<?php echo htmlspecialchars($idioma->getNombre() ?? ''); ?>">
                                    <i class="fa fa-trash-alt"></i>
                                </button>
                            </td>
                            <td><?php echo $idioma->getId(); ?></td>
                            <td class="idioma-nombre-cell"><?php echo htmlspecialchars($idioma->getNombre()); ?></td>
                            <td>
                                <?php if ($idioma->getEstado() == 1): ?>
                                    <span class="badge bg-success">Activo</span>
                                <?php else: ?>
                                    <span class="badge bg-danger">Inactivo</span>
                                <?php endif; ?>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                    </tbody>
                </table>
                <div id="empty-state" class="text-center py-4" style="display: none;">
                    <p class="text-muted">No se encontraron idiomas.</p>
                </div>
                <?php #endregion TABLE IDIOMAS ?>
            </div>
            <!-- END PANEL body -->
        </div>
        <?php #endregion PANEL IDIOMAS ?>

    </div>
    <!-- END #content -->

    <!-- Toast notification container -->
    <div class="toast-container" id="toast-container"></div>

    <?php #region region Crear Idioma Modal ?>
    <div class="modal fade" id="crearIdiomaModal" tabindex="-1" aria-labelledby="crearIdiomaModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="crearIdiomaModalLabel">Crear Nuevo Idioma</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form id="crear-idioma-form">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="crear_nombre" class="form-label">Nombre <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="crear_nombre" name="nombre" required>
                        </div>
                        <div id="crear-idioma-feedback"></div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                        <button type="submit" class="btn btn-success">Crear Idioma</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <?php #endregion Crear Idioma Modal ?>

    <?php #region region Editar Idioma Modal ?>
    <div class="modal fade" id="editarIdiomaModal" tabindex="-1" aria-labelledby="editarIdiomaModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editarIdiomaModalLabel">Editar Idioma</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form id="editar-idioma-form">
                    <input type="hidden" id="editar_idioma_id" name="idiomaId">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="editar_nombre" class="form-label">Nombre <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="editar_nombre" name="nombre" required>
                        </div>
                        <div id="editar-idioma-feedback"></div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                        <button type="submit" class="btn btn-primary">Actualizar Idioma</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <?php #endregion Editar Idioma Modal ?>

    <?php #region region Hidden Form for Deactivation ?>
    <form id="deactivate-idioma-form" method="POST" action="listado-idiomas" style="display: none;">
        <input type="hidden" name="action" value="desactivar">
        <input type="hidden" name="idiomaId" id="deactivate-idioma-id">
    </form>
    <?php #endregion Hidden Form ?>

    <!-- BEGIN scroll-top-btn -->
    <a href="#" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
    <!-- END scroll-top-btn -->
</div>
<!-- END #app -->

<?php #region region JS ?>
<?php require_once __ROOT__ . '/views/admin/general/core_js.view.php'; ?>

<!-- ================== BEGIN PAGE LEVEL JS ================== -->
<script>
    document.addEventListener('DOMContentLoaded', function () {
        console.log("Idiomas list page loaded.");

        // Get DOM elements
        const tableBody               = document.getElementById('idioma-table-body');
        const deactivateIdiomaForm    = document.getElementById('deactivate-idioma-form');
        const deactivateIdiomaIdInput = document.getElementById('deactivate-idioma-id');
        const crearIdiomaModal        = new bootstrap.Modal(document.getElementById('crearIdiomaModal'));
        const editarIdiomaModal       = new bootstrap.Modal(document.getElementById('editarIdiomaModal'));
        const crearIdiomaForm         = document.getElementById('crear-idioma-form');
        const editarIdiomaForm        = document.getElementById('editar-idioma-form');

        // Initialize empty state on page load
        updateEmptyState();

        // --- Table Event Handlers ---
        if (tableBody) {
            tableBody.addEventListener('click', function (event) {
                const editButton = event.target.closest('.btn-editar-idioma');
                const deactivateButton = event.target.closest('.btn-desactivar-idioma');

                // --- Handle Edit Click ---
                if (editButton) {
                    event.preventDefault();
                    const idiomaId = editButton.dataset.idiomaid;
                    const nombre = editButton.dataset.nombre;

                    // Populate edit modal
                    document.getElementById('editar_idioma_id').value = idiomaId;
                    document.getElementById('editar_nombre').value = nombre;

                    // Clear previous feedback
                    document.getElementById('editar-idioma-feedback').innerHTML = '';

                    // Show modal
                    editarIdiomaModal.show();
                }

                // --- Handle Deactivate Click ---
                if (deactivateButton) {
                    event.preventDefault();
                    const idiomaId = deactivateButton.dataset.idiomaid;
                    const nombre = deactivateButton.dataset.nombre;

                    // Confirm before deactivating using SweetAlert
                    swal({
                        title: "Confirmar Desactivación",
                        text: `¿Está seguro que desea desactivar el idioma "${nombre}"?`,
                        icon: "warning",
                        buttons: {
                            cancel: {text: "Cancelar", value: null, visible: true, className: "", closeModal: true},
                            confirm: {text: "Confirmar", value: true, visible: true, className: "btn-danger", closeModal: true}
                        },
                    }).then((willDeactivate) => {
                        if (willDeactivate) {
                            deactivateIdiomaIdInput.value = idiomaId;
                            deactivateIdiomaForm.submit();
                        }
                    });
                }
            });
        }

        // --- Create Idioma Form Submission ---
        if (crearIdiomaForm) {
            crearIdiomaForm.addEventListener('submit', function(event) {
                event.preventDefault();

                const formData = new FormData(crearIdiomaForm);
                formData.append('action', 'crear');

                const feedback = document.getElementById('crear-idioma-feedback');
                feedback.innerHTML = '<div class="alert alert-info">Creando idioma... <span class="spinner-border spinner-border-sm"></span></div>';

                fetch('listado-idiomas', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Success - close modal and show toast
                        crearIdiomaModal.hide();
                        crearIdiomaForm.reset();
                        showToastNotification(data.message, 'success');

                        // Add new row to table
                        addIdiomaToTable(data.idioma);
                        updateEmptyState();
                    } else {
                        // Error
                        feedback.innerHTML = `<div class="alert alert-danger">${data.message}</div>`;
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    feedback.innerHTML = '<div class="alert alert-danger">Error de comunicación. Inténtelo de nuevo.</div>';
                });
            });
        }

        // --- Edit Idioma Form Submission ---
        if (editarIdiomaForm) {
            editarIdiomaForm.addEventListener('submit', function(event) {
                event.preventDefault();

                const formData = new FormData(editarIdiomaForm);
                formData.append('action', 'modificar');

                const feedback = document.getElementById('editar-idioma-feedback');
                feedback.innerHTML = '<div class="alert alert-info">Actualizando idioma... <span class="spinner-border spinner-border-sm"></span></div>';

                fetch('listado-idiomas', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Success - close modal and show toast
                        editarIdiomaModal.hide();
                        showToastNotification(data.message, 'success');

                        // Update row in table
                        updateIdiomaInTable(data.idioma);
                    } else {
                        // Error
                        feedback.innerHTML = `<div class="alert alert-danger">${data.message}</div>`;
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    feedback.innerHTML = '<div class="alert alert-danger">Error de comunicación. Inténtelo de nuevo.</div>';
                });
            });
        }

        // --- Toast Notification Functions ---
        function showToastNotification(message, type = 'success') {
            const toastContainer = document.getElementById('toast-container');
            if (!toastContainer) return;

            const toastId = 'toast-' + Date.now();
            const toastHtml = `
                <div class="toast toast-${type}" id="${toastId}" role="alert" aria-live="assertive" aria-atomic="true" data-bs-autohide="true" data-bs-delay="4000">
                    <div class="toast-header">
                        <i class="fa ${type === 'success' ? 'fa-check-circle text-success' : 'fa-exclamation-circle text-danger'} me-2"></i>
                        <strong class="me-auto">${type === 'success' ? 'Éxito' : 'Error'}</strong>
                        <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
                    </div>
                    <div class="toast-body">
                        ${message}
                    </div>
                </div>
            `;

            toastContainer.insertAdjacentHTML('beforeend', toastHtml);
            const toastElement = document.getElementById(toastId);
            const toast = new bootstrap.Toast(toastElement);
            toast.show();

            // Remove toast element after it's hidden
            toastElement.addEventListener('hidden.bs.toast', () => {
                toastElement.remove();
            });
        }

        // --- Table Update Functions ---
        function addIdiomaToTable(idioma) {
            const tableBody = document.getElementById('idioma-table-body');
            if (!tableBody) return;

            const newRow = document.createElement('tr');
            newRow.setAttribute('data-idioma-id', idioma.id);
            newRow.innerHTML = `
                <td>
                    <button type="button" class="btn btn-xs btn-primary me-1 btn-editar-idioma"
                            title="Editar Idioma"
                            data-idiomaid="${idioma.id}"
                            data-nombre="${escapeHtml(idioma.nombre)}">
                        <i class="fa fa-edit"></i>
                    </button>
                    <button type="button" class="btn btn-xs btn-danger btn-desactivar-idioma"
                            title="Desactivar"
                            data-idiomaid="${idioma.id}"
                            data-nombre="${escapeHtml(idioma.nombre)}">
                        <i class="fa fa-trash-alt"></i>
                    </button>
                </td>
                <td>${idioma.id}</td>
                <td class="idioma-nombre-cell">${escapeHtml(idioma.nombre)}</td>
                <td><span class="badge bg-success">Activo</span></td>
            `;

            tableBody.appendChild(newRow);
        }

        function updateIdiomaInTable(idioma) {
            const row = document.querySelector(`tr[data-idioma-id="${idioma.id}"]`);
            if (!row) return;

            // Update data attributes in buttons
            const editButton = row.querySelector('.btn-editar-idioma');
            const deleteButton = row.querySelector('.btn-desactivar-idioma');

            if (editButton) {
                editButton.setAttribute('data-nombre', idioma.nombre);
            }

            if (deleteButton) {
                deleteButton.setAttribute('data-nombre', idioma.nombre);
            }

            // Update table cells
            const nombreCell = row.querySelector('.idioma-nombre-cell');
            if (nombreCell) nombreCell.textContent = idioma.nombre;
        }

        // --- Empty State Management ---
        function updateEmptyState() {
            const tableBody = document.getElementById('idioma-table-body');
            const emptyState = document.getElementById('empty-state');

            if (!tableBody || !emptyState) return;

            const hasRows = tableBody.children.length > 0;

            if (hasRows) {
                emptyState.style.display = 'none';
            } else {
                emptyState.style.display = 'block';
            }
        }

        // --- Utility Functions ---
        function escapeHtml(text) {
            if (text === null || text === undefined) {
                return '';
            }
            const map = {
                '&': '&amp;',
                '<': '&lt;',
                '>': '&gt;',
                '"': '&quot;',
                "'": '&#039;'
            };
            return String(text).replace(/[&<>"']/g, function(m) { return map[m]; });
        }

    });
</script>
<!-- ================== END PAGE LEVEL JS ================== -->
<?php #endregion JS ?>
</body>
</html>
