<?php

declare(strict_types=1);

namespace App\classes;

// Importar clases necesarias
use App\classes\Freelancer;
use App\classes\Idioma;
use Exception;
use PDO;
use PDOException;

class FreelancerIdioma
{
    // --- Atributos ---
    private ?int    $id              = null;
    private ?int    $id_freelancer   = null;
    private ?int    $id_idioma       = null;
    private ?string $nivel           = null;

    /**
     * Constructor: Inicializa las propiedades del objeto FreelancerIdioma.
     */
    public function __construct()
    {
        $this->id            = 0;
        $this->id_freelancer = null;
        $this->id_idioma     = null;
        $this->nivel         = null;
    }

    /**
     * Método estático para construir un objeto FreelancerIdioma desde un array (ej. fila de DB).
     *
     * @param array $resultado Array asociativo con los datos del freelancer_idioma.
     *
     * @return self Instancia de FreelancerIdioma.
     * @throws Exception Si ocurre un error durante la construcción.
     */
    public static function construct(array $resultado = []): self
    {
        try {
            $objeto = new self();
            $objeto->id            = isset($resultado['id']) ? (int)$resultado['id'] : 0;
            $objeto->id_freelancer = isset($resultado['id_freelancer']) ? (int)$resultado['id_freelancer'] : null;
            $objeto->id_idioma     = isset($resultado['id_idioma']) ? (int)$resultado['id_idioma'] : null;
            $objeto->nivel         = $resultado['nivel'] ?? null;
            return $objeto;
        } catch (Exception $e) {
            throw new Exception("Error al construir FreelancerIdioma: " . $e->getMessage());
        }
    }

    // --- Métodos de Acceso a Datos (Estáticos y de Instancia) ---

    /**
     * Crea un nuevo registro en la base de datos a partir de un objeto FreelancerIdioma.
     *
     * @param PDO $conexion Conexión PDO.
     *
     * @return int|false El ID del nuevo registro creado o false en caso de error.
     * @throws Exception Si los datos requeridos en el objeto están vacíos o hay error en DB.
     */
    public function crear(PDO $conexion): int|false
    {
        if (empty($this->getIdFreelancer()) || empty($this->getIdIdioma())) {
            throw new Exception("ID de freelancer e ID de idioma son requeridos para crear un registro FreelancerIdioma.");
        }

        try {
            $query = <<<SQL
            INSERT INTO freelancers_idiomas (
                 id_freelancer
                ,id_idioma
                ,nivel
            ) VALUES (
                 :id_freelancer_param
                ,:id_idioma_param
                ,:nivel_param
            )
            SQL;

            $statement = $conexion->prepare($query);

            $statement->bindValue(':id_freelancer_param', $this->getIdFreelancer(), PDO::PARAM_INT);
            $statement->bindValue(':id_idioma_param', $this->getIdIdioma(), PDO::PARAM_INT);
            $statement->bindValue(':nivel_param', $this->getNivel(), PDO::PARAM_STR);

            $success = $statement->execute();

            if ($success) {
                return (int)$conexion->lastInsertId();
            } else {
                return false;
            }

        } catch (PDOException $e) {
            throw new Exception("Error de base de datos al crear FreelancerIdioma: " . $e->getMessage());
        } catch (Exception $e) {
            throw new Exception("Error al crear FreelancerIdioma: " . $e->getMessage());
        }
    }

    /**
     * Actualiza un registro existente en la base de datos a partir de un objeto FreelancerIdioma.
     *
     * @param PDO $conexion Conexión PDO.
     *
     * @return bool True si la actualización fue exitosa, False en caso contrario.
     * @throws Exception Si el ID del objeto está vacío o hay error en DB.
     */
    public function actualizar(PDO $conexion): bool
    {
        if (empty($this->getId())) {
            throw new Exception("ID es requerido para actualizar un registro FreelancerIdioma.");
        }

        try {
            $query = <<<SQL
            UPDATE freelancers_idiomas SET
                 id_freelancer = :id_freelancer_update
                ,id_idioma     = :id_idioma_update
                ,nivel         = :nivel_update
            WHERE
                id = :id_update
            SQL;

            $statement = $conexion->prepare($query);

            $statement->bindValue(':id_freelancer_update', $this->getIdFreelancer(), PDO::PARAM_INT);
            $statement->bindValue(':id_idioma_update', $this->getIdIdioma(), PDO::PARAM_INT);
            $statement->bindValue(':nivel_update', $this->getNivel(), PDO::PARAM_STR);
            $statement->bindValue(':id_update', $this->getId(), PDO::PARAM_INT);

            return $statement->execute();

        } catch (PDOException $e) {
            throw new Exception("Error de base de datos al actualizar FreelancerIdioma (ID: {$this->getId()}): " . $e->getMessage());
        }
    }

    /**
     * Elimina un registro de la base de datos por su ID.
     *
     * @param int $id       ID del registro a eliminar.
     * @param PDO $conexion Conexión PDO.
     *
     * @return bool True si la eliminación fue exitosa, False en caso contrario.
     * @throws Exception Si ocurre un error de base de datos.
     */
    public static function eliminar(int $id, PDO $conexion): bool
    {
        try {
            $query = <<<SQL
            DELETE FROM freelancers_idiomas
            WHERE
                id = :id_param
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(':id_param', $id, PDO::PARAM_INT);

            return $statement->execute();

        } catch (PDOException $e) {
            throw new Exception("Error de base de datos al eliminar FreelancerIdioma (ID: $id): " . $e->getMessage());
        }
    }

    /**
     * Obtiene un registro FreelancerIdioma por su ID.
     *
     * @param int $id       ID del registro.
     * @param PDO $conexion Conexión PDO.
     *
     * @return self|null Objeto FreelancerIdioma o null si no se encuentra.
     * @throws Exception Si hay error en DB.
     */
    public static function get_by_id(int $id, PDO $conexion): ?self
    {
        try {
            $query = <<<SQL
            SELECT
                *
            FROM freelancers_idiomas
            WHERE
                id = :id_param
            LIMIT 1
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(":id_param", $id, PDO::PARAM_INT);
            $statement->execute();
            $resultado = $statement->fetch(PDO::FETCH_ASSOC);

            return $resultado ? self::construct($resultado) : null;

        } catch (PDOException $e) {
            throw new Exception("Error al obtener FreelancerIdioma (ID: $id): " . $e->getMessage());
        }
    }

    /**
     * Obtiene una lista de todos los registros FreelancerIdioma.
     *
     * @param PDO $conexion Conexión PDO.
     *
     * @return array Array de objetos FreelancerIdioma.
     * @throws Exception Si hay error en DB.
     */
    public static function get_list(PDO $conexion): array
    {
        try {
            $query = <<<SQL
            SELECT
                *
            FROM freelancers_idiomas
            ORDER BY
                id_freelancer, id_idioma
            SQL;

            $statement = $conexion->prepare($query);
            $statement->execute();
            $resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

            $listado = [];
            foreach ($resultados as $resultado) {
                $listado[] = self::construct($resultado);
            }
            return $listado;

        } catch (PDOException $e) {
            throw new Exception("Error al obtener lista de FreelancerIdiomas: " . $e->getMessage());
        }
    }

    /**
     * Obtiene una lista de registros FreelancerIdioma por ID de freelancer.
     *
     * @param int $freelancerId ID del freelancer.
     * @param PDO $conexion     Conexión PDO.
     *
     * @return array Array de objetos FreelancerIdioma.
     * @throws Exception Si hay error en DB.
     */
    public static function get_by_freelancer_id(int $freelancerId, PDO $conexion): array
    {
        try {
            $query = <<<SQL
            SELECT
                *
            FROM freelancers_idiomas
            WHERE
                id_freelancer = :freelancer_id_param
            ORDER BY
                id_idioma
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(':freelancer_id_param', $freelancerId, PDO::PARAM_INT);
            $statement->execute();
            $resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

            $listado = [];
            foreach ($resultados as $resultado) {
                $listado[] = self::construct($resultado);
            }
            return $listado;

        } catch (PDOException $e) {
            throw new Exception("Error al obtener FreelancerIdiomas por ID de freelancer ($freelancerId): " . $e->getMessage());
        }
    }

    /**
     * Obtiene una lista de registros FreelancerIdioma por ID de idioma.
     *
     * @param int $idiomaId ID del idioma.
     * @param PDO $conexion Conexión PDO.
     *
     * @return array Array de objetos FreelancerIdioma.
     * @throws Exception Si hay error en DB.
     */
    public static function get_by_idioma_id(int $idiomaId, PDO $conexion): array
    {
        try {
            $query = <<<SQL
            SELECT
                *
            FROM freelancers_idiomas
            WHERE
                id_idioma = :idioma_id_param
            ORDER BY
                id_freelancer
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(':idioma_id_param', $idiomaId, PDO::PARAM_INT);
            $statement->execute();
            $resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

            $listado = [];
            foreach ($resultados as $resultado) {
                $listado[] = self::construct($resultado);
            }
            return $listado;

        } catch (PDOException $e) {
            throw new Exception("Error al obtener FreelancerIdiomas por ID de idioma ($idiomaId): " . $e->getMessage());
        }
    }

    /**
     * Obtiene el objeto Freelancer asociado a este registro.
     * (Asume que existe una clase Freelancer con un método get(id, conexion))
     *
     * @param PDO $conexion Conexión PDO.
     *
     * @return object|null Objeto Freelancer o null si no se encuentra.
     * @throws Exception Si hay error en DB o la clase Freelancer no existe.
     */
    public function get_freelancer(PDO $conexion): ?object
    {
        if (empty($this->getIdFreelancer())) {
            return null;
        }
        
        return Freelancer::get($this->getIdFreelancer(), $conexion);
    }

    /**
     * Obtiene el objeto Idioma asociado a este registro.
     * (Asume que existe una clase Idioma con un método get(id, conexion))
     *
     * @param PDO $conexion Conexión PDO.
     *
     * @return object|null Objeto Idioma o null si no se encuentra.
     * @throws Exception Si hay error en DB o la clase Idioma no existe.
     */
    public function get_idioma(PDO $conexion): ?object
    {
        if (empty($this->getIdIdioma())) {
            return null;
        }
        
        return Idioma::get_by_id($this->getIdIdioma(), $conexion);
    }


    // --- Getters y Setters ---

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(?int $id): self
    {
        $this->id = $id;
        return $this;
    }

    public function getIdFreelancer(): ?int
    {
        return $this->id_freelancer;
    }

    public function setIdFreelancer(?int $id_freelancer): self
    {
        $this->id_freelancer = $id_freelancer;
        return $this;
    }

    public function getIdIdioma(): ?int
    {
        return $this->id_idioma;
    }

    public function setIdIdioma(?int $id_idioma): self
    {
        $this->id_idioma = $id_idioma;
        return $this;
    }

    public function getNivel(): ?string
    {
        return $this->nivel;
    }

    public function setNivel(?string $nivel): self
    {
        $this->nivel = $nivel;
        return $this;
    }
}
