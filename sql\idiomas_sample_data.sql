-- Insert sample language data for testing
-- This script adds common languages to the idiomas table

INSERT INTO idiomas (nombre, estado) VALUES 
('Español', 1),
('Inglés', 1),
('<PERSON><PERSON><PERSON><PERSON>', 1),
('<PERSON>em<PERSON>', 1),
('Italiano', 1),
('Portugués', 1),
('<PERSON>us<PERSON>', 1),
('Chino Mandarín', 1),
('Japonés', 1),
('Coreano', 1),
('<PERSON>rab<PERSON>', 1),
('Hindi', 1),
('Holandés', 1),
('<PERSON><PERSON>', 1),
('Noruego', 1),
('<PERSON><PERSON>', 1),
('Finlandés', 1),
('Polaco', 1),
('Checo', 1),
('<PERSON><PERSON><PERSON><PERSON>', 1),
('<PERSON><PERSON><PERSON>', 1),
('<PERSON><PERSON><PERSON>', 1),
('<PERSON><PERSON><PERSON>', 1),
('Tailandés', 1),
('Vietnamita', 1),
('Indonesio', 1),
('Malayo', 1),
('<PERSON>alo', 1),
('Swahili', 1),
('<PERSON>al<PERSON>', 1)
ON DUPLICATE KEY UPDATE estado = 1;
